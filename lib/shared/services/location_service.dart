/// Location Service
///
/// Provides methods for handling location-related operations
/// Centralizes location access, permissions, and geocoding
///
/// This service handles location permissions, fetching, and geocoding
library location_service;

import 'package:flutter/foundation.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:location/location.dart' as location_lib;
import 'package:permission_handler/permission_handler.dart';

/// Location data model
///
/// Contains location coordinates and address information
class LocationData {
  final double lat;
  final double lng;
  final String country;
  final String city;
  final String district;

  LocationData({
    required this.lat,
    required this.lng,
    required this.country,
    required this.city,
    required this.district,
  });

  /// Convert to a map for storage or API calls
  Map<String, dynamic> toMap() {
    return {
      'lat': lat,
      'lng': lng,
      'country': country,
      'city': city,
      'district': district,
    };
  }
}

/// Location Service Interface
///
/// Defines the contract for location operations
/// Allows for easy mocking in tests
abstract class LocationService {
  /// Check if location services are enabled
  ///
  /// @return A Future that resolves to true if enabled, false otherwise
  Future<bool> isLocationServiceEnabled();

  /// Check if location permission is granted
  ///
  /// @return A Future that resolves to true if granted, false otherwise
  Future<bool> isLocationPermissionGranted();

  /// Request location permission
  ///
  /// @return A Future that resolves to true if granted, false otherwise
  Future<bool> requestLocationPermission();

  /// Get the current location
  ///
  /// @return A Future that resolves to a LocationData object
  /// @throws Exception if location services are disabled or permission is denied
  Future<LocationData> getCurrentLocation();

  /// Get a mock location for testing or simulator use
  ///
  /// @return A LocationData object with mock values
  LocationData getMockLocation();
}

/// Location Service Implementation
///
/// Implements the LocationService interface using location and geocoding packages
class LocationServiceImpl implements LocationService {
  final location_lib.Location _location;

  /// Constructor that takes a Location instance
  ///
  /// @param location The Location instance to use
  LocationServiceImpl({
    required location_lib.Location location,
  }) : _location = location;

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await _location.serviceEnabled();
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location service: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> isLocationPermissionGranted() async {
    try {
      location_lib.PermissionStatus permissionStatus =
          await _location.hasPermission();
      return permissionStatus == location_lib.PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location permission: $e');
      }
      return false;
    }
  }

  @override
  Future<bool> requestLocationPermission() async {
    try {
      // Check if service is enabled
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          return false;
        }
      }

      // Check permission
      location_lib.PermissionStatus permissionStatus =
          await _location.hasPermission();
      if (permissionStatus == location_lib.PermissionStatus.denied) {
        permissionStatus = await _location.requestPermission();
        if (permissionStatus != location_lib.PermissionStatus.granted) {
          // Try with permission_handler as a fallback
          if (await Permission.location.request().isGranted) {
            return true;
          }
          return false;
        }
      }

      return permissionStatus == location_lib.PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting location permission: $e');
      }
      return false;
    }
  }

  @override
  Future<LocationData> getCurrentLocation() async {
    try {
      if (kDebugMode) {
        print('LocationService: Starting location retrieval');
      }

      // Using real GPS location from device

      // Check if service is enabled
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) {
          print('LocationService: Location service not enabled, requesting...');
        }
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          throw Exception('Location services are disabled');
        }
      }

      // Check permission
      bool permissionGranted = await isLocationPermissionGranted();
      if (!permissionGranted) {
        if (kDebugMode) {
          print('LocationService: Permission not granted, requesting...');
        }
        permissionGranted = await requestLocationPermission();
        if (!permissionGranted) {
          throw Exception('Location permission denied');
        }
      }

      if (kDebugMode) {
        print('LocationService: Getting GPS coordinates...');
        print('LocationService: Configuring location settings...');
      }

      // Configure location settings for better performance
      await _location.changeSettings(
        accuracy: location_lib.LocationAccuracy.balanced,
        interval: 1000,
        distanceFilter: 10,
      );

      if (kDebugMode) {
        print(
            'LocationService: Attempting to get location with 30 second timeout...');
      }

      // Get location with reasonable timeout and better settings
      location_lib.LocationData? position;
      try {
        if (kDebugMode) {
          print('LocationService: About to call _location.getLocation()...');
        }

        // Try different location strategies
        if (kDebugMode) {
          print('LocationService: Trying to get location...');
        }

        // Strategy 1: Try to get current location with minimal timeout
        try {
          if (kDebugMode) {
            print('LocationService: Attempting quick location (5 seconds)...');
          }

          position = await _location.getLocation().timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              throw Exception('Quick location timeout');
            },
          );

          if (kDebugMode) {
            print('LocationService: Got location quickly');
          }
        } catch (quickError) {
          if (kDebugMode) {
            print('LocationService: Quick location failed: $quickError');
            print('LocationService: Trying with different settings...');
          }

          // Strategy 2: Try with high accuracy but longer timeout
          try {
            await _location.changeSettings(
              accuracy: location_lib.LocationAccuracy.high,
              interval: 5000,
              distanceFilter: 0,
            );

            if (kDebugMode) {
              print(
                  'LocationService: Attempting high accuracy location (15 seconds)...');
            }

            position = await _location.getLocation().timeout(
              const Duration(seconds: 15),
              onTimeout: () {
                throw Exception('High accuracy location timeout');
              },
            );

            if (kDebugMode) {
              print('LocationService: Got high accuracy location');
            }
          } catch (highAccuracyError) {
            if (kDebugMode) {
              print(
                  'LocationService: High accuracy failed: $highAccuracyError');
              print('LocationService: Trying low accuracy as last resort...');
            }

            // Strategy 3: Try with low accuracy as last resort
            await _location.changeSettings(
              accuracy: location_lib.LocationAccuracy.low,
              interval: 10000,
              distanceFilter: 100,
            );

            if (kDebugMode) {
              print(
                  'LocationService: Attempting low accuracy location (10 seconds)...');
            }

            position = await _location.getLocation().timeout(
              const Duration(seconds: 10),
              onTimeout: () {
                throw Exception(
                    'All location strategies failed - GPS may be unavailable');
              },
            );

            if (kDebugMode) {
              print('LocationService: Got low accuracy location');
            }
          }
        }

        if (kDebugMode) {
          print('LocationService: GPS location obtained successfully');
          print('LocationService: Latitude: ${position.latitude}');
          print('LocationService: Longitude: ${position.longitude}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('LocationService: GPS location failed: $e');
          print('LocationService: Unable to get device location');
        }
        // Rethrow the error instead of using mock location
        throw Exception('Failed to get GPS location: $e');
      }

      if (kDebugMode) {
        print(
            'LocationService: GPS coordinates obtained: ${position.latitude}, ${position.longitude}');
        print('LocationService: Starting reverse geocoding...');
      }

      // Set locale for Arabic place names
      geo.setLocaleIdentifier("ar_SA");

      // Convert coordinates to human-readable location with timeout
      List<geo.Placemark> placemarks = await geo
          .placemarkFromCoordinates(
        position.latitude!,
        position.longitude!,
      )
          .timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          if (kDebugMode) {
            print(
                'LocationService: Geocoding timed out, using coordinates only');
          }
          // Return empty placemarks if geocoding times out
          return <geo.Placemark>[];
        },
      );

      // Extract address components
      String country = '';
      String city = '';
      String district = '';

      if (placemarks.isNotEmpty) {
        country = placemarks.first.country ?? '';
        city = placemarks.first.locality ?? '';
        district = placemarks.first.subLocality ?? '';

        if (kDebugMode) {
          print('LocationService: Reverse geocoding completed');
          print('LocationService: Country: $country');
          print('LocationService: City: $city');
          print('LocationService: District: $district');
        }
      } else {
        if (kDebugMode) {
          print(
              'LocationService: No geocoding results, using coordinates only');
        }
        // Use default values for Saudi Arabia if geocoding fails
        country = 'المملكة العربية السعودية';
        city = 'موقع غير محدد';
        district = 'حي غير محدد';
      }

      return LocationData(
        lat: position.latitude!,
        lng: position.longitude!,
        country: country,
        city: city,
        district: district,
      );
    } catch (e) {
      if (kDebugMode) {
        print('LocationService: Error getting location: $e');
      }

      // Always rethrow the error to let the caller handle it
      rethrow;
    }
  }

  @override
  LocationData getMockLocation() {
    return LocationData(
      lat: 24.680459665769533,
      lng: 46.579983324072145,
      country: "المملكة العربية السعودية",
      city: "الرياض",
      district: "عرقة",
    );
  }
}
