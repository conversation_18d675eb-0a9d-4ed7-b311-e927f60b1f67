/// Location Provider
///
/// Provides Riverpod state management for location functionality
/// Replaces LocationViewModel with Riverpod architecture
library location_provider;

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/core/providers/navigation_provider.dart';
import 'package:towasl/core/providers/service_providers.dart';
import 'package:towasl/core/routes.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/features/profile/presentation/providers/user_repository_provider.dart';
import 'package:towasl/features/core/presentation/providers/app_state_provider.dart';
import 'package:towasl/shared/models/user_model.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';
import 'package:towasl/shared/providers/district_provider.dart';
import 'package:towasl/shared/providers/city_validation_provider.dart';
import 'package:towasl/shared/services/district_service.dart'
    as district_service;

part 'location_provider.g.dart';

/// State class for location management
class LocationState {
  /// Current latitude
  final double latitude;

  /// Current longitude
  final double longitude;

  /// Country name
  final String country;

  /// City name
  final String city;

  /// District name
  final String district;

  /// Whether location is loading
  final bool isLoading;

  /// Whether location is being saved
  final bool isSaving;

  /// Whether location permission is granted
  final bool hasLocationPermission;

  /// Whether app settings dialog is open
  final bool isOpenAppSetting;

  /// Whether city supports district selection
  final bool citySupportsDistricts;

  /// Available districts for current city
  final List<district_service.District> availableDistricts;

  /// Whether districts are being loaded
  final bool isLoadingDistricts;

  /// Whether district checking is complete (used to control when to show location)
  final bool districtCheckComplete;

  /// Error message if any
  final String? errorMessage;

  const LocationState({
    this.latitude = 0.0,
    this.longitude = 0.0,
    this.country = '',
    this.city = '',
    this.district = '',
    this.isLoading = false,
    this.isSaving = false,
    this.hasLocationPermission = false,
    this.isOpenAppSetting = false,
    this.citySupportsDistricts = false,
    this.availableDistricts = const [],
    this.isLoadingDistricts = false,
    this.districtCheckComplete = false,
    this.errorMessage,
  });

  LocationState copyWith({
    double? latitude,
    double? longitude,
    String? country,
    String? city,
    String? district,
    bool? isLoading,
    bool? isSaving,
    bool? hasLocationPermission,
    bool? isOpenAppSetting,
    bool? citySupportsDistricts,
    List<district_service.District>? availableDistricts,
    bool? isLoadingDistricts,
    bool? districtCheckComplete,
    String? errorMessage,
  }) {
    return LocationState(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      country: country ?? this.country,
      city: city ?? this.city,
      district: district ?? this.district,
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      hasLocationPermission:
          hasLocationPermission ?? this.hasLocationPermission,
      isOpenAppSetting: isOpenAppSetting ?? this.isOpenAppSetting,
      citySupportsDistricts:
          citySupportsDistricts ?? this.citySupportsDistricts,
      availableDistricts: availableDistricts ?? this.availableDistricts,
      isLoadingDistricts: isLoadingDistricts ?? this.isLoadingDistricts,
      districtCheckComplete:
          districtCheckComplete ?? this.districtCheckComplete,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Clear error message
  LocationState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Check if location data is available
  bool get hasLocationData {
    return latitude != 0.0 && longitude != 0.0 && city.isNotEmpty;
  }
}

/// Location Notifier
///
/// Manages location state and business logic
/// Follows MVERPOD pattern with Riverpod state management
@Riverpod(keepAlive: true)
class Location extends _$Location {
  @override
  LocationState build() {
    if (kDebugMode) {
      print('LocationNotifier: Initialized');
    }

    // Check permission status on initialization
    _checkInitialPermissionStatus();

    return const LocationState();
  }

  /// Check initial permission status
  Future<void> _checkInitialPermissionStatus() async {
    try {
      final locationService = ref.read(locationServiceProvider);
      final hasPermission = await locationService.isLocationPermissionGranted();

      if (kDebugMode) {
        print('LocationNotifier: Initial permission status: $hasPermission');
      }

      state = state.copyWith(hasLocationPermission: hasPermission);
    } catch (e) {
      if (kDebugMode) {
        print('LocationNotifier: Error checking initial permission: $e');
      }
      // Keep default false state
    }
  }

  /// Load user's existing location
  Future<void> loadUserLocation(String userId) async {
    if (kDebugMode) {
      print('LocationNotifier: Loading user location for: $userId');
    }

    try {
      // Don't set isLoading: true when loading existing user location
      // This is different from actively getting current GPS location

      // Load user data directly from repository to avoid provider dependency issues
      final userRepository = ref.read(userRepositoryProvider);
      final userModel = await userRepository.getUserById(userId);

      if (userModel?.userLocation != null) {
        final location = userModel!.userLocation!;
        state = state.copyWith(
          latitude: location.lat ?? 0.0,
          longitude: location.lng ?? 0.0,
          country: location.country ?? '',
          city: location.city ?? '',
          district: location.district ?? '',
          districtCheckComplete: false, // Reset district check status
        );

        // Check and load districts for the city
        await _checkAndLoadDistricts();

        if (kDebugMode) {
          print('LocationNotifier: User location loaded: ${location.city}');
          print('LocationNotifier: Location details:');
          print('  - City: ${location.city}');
          print('  - Country: ${location.country}');
          print('  - District: ${location.district}');
          print('  - Lat/Lng: ${location.lat}, ${location.lng}');
        }
      } else {
        // No existing location found - start with empty state
        state = state.copyWith(
          latitude: 0.0,
          longitude: 0.0,
          country: '',
          city: '',
          district: '',
          districtCheckComplete:
              true, // No district check needed for empty state
        );

        if (kDebugMode) {
          print(
              'LocationNotifier: No existing location found for user: $userId');
        }
      }
    } catch (e) {
      // Handle errors gracefully
      state = state.copyWith(
        latitude: 0.0,
        longitude: 0.0,
        country: '',
        city: '',
        district: '',
        districtCheckComplete: true, // No district check needed for error state
        errorMessage: null, // Don't show error for initialization issues
      );

      if (kDebugMode) {
        print('LocationNotifier: Error loading user location - $e');
        print('LocationNotifier: Starting with empty location state');
      }
    }
  }

  /// Check current permission status
  Future<void> checkPermissionStatus() async {
    try {
      final locationService = ref.read(locationServiceProvider);
      final hasPermission = await locationService.isLocationPermissionGranted();

      if (kDebugMode) {
        print('LocationNotifier: Current permission status: $hasPermission');
      }

      state = state.copyWith(hasLocationPermission: hasPermission);
    } catch (e) {
      if (kDebugMode) {
        print('LocationNotifier: Error checking permission status: $e');
      }
    }
  }

  /// Request location permission
  Future<void> requestLocationPermission() async {
    if (kDebugMode) {
      print('LocationNotifier: Requesting location permission');
    }

    try {
      // Use real location service to request permission
      final locationService = ref.read(locationServiceProvider);
      final permissionGranted =
          await locationService.requestLocationPermission();

      state = state.copyWith(hasLocationPermission: permissionGranted);

      if (kDebugMode) {
        print(
            'LocationNotifier: Location permission result: $permissionGranted');
      }

      if (permissionGranted) {
        // Automatically get current location after permission is granted
        await getCurrentLocation();
      } else {
        state = state.copyWith(
          errorMessage: 'Location permission denied',
        );
        ToastCustom.errorToast('Location permission denied');
      }
    } catch (e) {
      state = state.copyWith(
        hasLocationPermission: false,
        errorMessage: 'Failed to get location permission',
      );

      if (kDebugMode) {
        print('LocationNotifier: Error requesting location permission - $e');
      }

      ToastCustom.errorToast('Failed to get location permission');
    }
  }

  /// Get current location
  Future<void> getCurrentLocation() async {
    if (kDebugMode) {
      print('LocationNotifier: Getting current location');
    }

    try {
      state = state.copyWith(isLoading: true);

      // Use real location service with timeout (it will handle permissions internally)
      final locationService = ref.read(locationServiceProvider);

      if (kDebugMode) {
        print('LocationNotifier: Calling location service...');
      }

      final locationData = await locationService.getCurrentLocation().timeout(
        const Duration(seconds: 75),
        onTimeout: () {
          if (kDebugMode) {
            print(
                'LocationNotifier: Location service timed out after 75 seconds');
          }
          throw Exception('Location service timed out - please try again');
        },
      );

      if (kDebugMode) {
        print('LocationNotifier: Location service returned data');
      }

      // Update permission state if location was obtained successfully
      state = state.copyWith(hasLocationPermission: true);

      // Update state with real location data
      state = state.copyWith(
        latitude: locationData.lat,
        longitude: locationData.lng,
        country: locationData.country,
        city: locationData.city,
        district: locationData.district,
        isLoading: false,
        districtCheckComplete: false, // Reset district check status
      );

      if (kDebugMode) {
        print('LocationNotifier: Current location obtained');
        print('LocationNotifier: Location details:');
        print('  - City: ${locationData.city}');
        print('  - Country: ${locationData.country}');
        print('  - District: ${locationData.district}');
        print('  - Lat/Lng: ${locationData.lat}, ${locationData.lng}');
      }

      // Check if city supports districts and load them
      await _checkAndLoadDistricts();

      // Try to match GPS district with Firestore district to get coordinates
      await _matchGpsDistrictWithFirestore();

      ToastCustom.successToast('تم جلب الموقع');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to get current location',
      );

      if (kDebugMode) {
        print('LocationNotifier: Error getting current location - $e');
      }

      // Provide more specific error messages
      String errorMessage = 'فشل في الحصول على الموقع';
      if (e.toString().contains('timeout')) {
        errorMessage = 'انتهت مهلة الحصول على الموقع - تأكد من تفعيل GPS';
      } else if (e.toString().contains('permission')) {
        errorMessage = 'يرجى السماح للتطبيق بالوصول للموقع';
      } else if (e.toString().contains('disabled')) {
        errorMessage = 'يرجى تفعيل خدمات الموقع في الجهاز';
      }

      ToastCustom.errorToast(errorMessage);
    }
  }

  /// Update location manually
  void updateLocation({
    double? latitude,
    double? longitude,
    String? country,
    String? city,
    String? district,
  }) {
    state = state.copyWith(
      latitude: latitude ?? state.latitude,
      longitude: longitude ?? state.longitude,
      country: country ?? state.country,
      city: city ?? state.city,
      district: district ?? state.district,
    );

    if (kDebugMode) {
      print('LocationNotifier: Location updated manually');
    }
  }

  /// Save location
  Future<void> saveLocation({bool isFromSetting = false}) async {
    if (!state.hasLocationData) {
      state =
          state.copyWith(errorMessage: 'Please select or get your location');
      ToastCustom.errorToast('Please select or get your location');
      return;
    }

    // Try to get user ID from multiple sources
    String userId = '';

    // First try from app state provider
    try {
      userId = ref.read(userIdProvider);
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocationNotifier: Could not get user ID from app state provider - $e');
      }
    }

    // If still empty, try from storage service directly
    if (userId.isEmpty) {
      try {
        final storageService = ref.read(storageServiceProvider);
        userId = storageService.getUserIDValue();
        if (kDebugMode) {
          print('LocationNotifier: Got user ID from storage: $userId');
        }
      } catch (e) {
        if (kDebugMode) {
          print('LocationNotifier: Could not get user ID from storage - $e');
        }
      }
    }

    if (userId.isEmpty) {
      state = state.copyWith(errorMessage: 'User not found');
      ToastCustom.errorToast('User not found');
      if (kDebugMode) {
        print('LocationNotifier: No user ID found from any source');
      }
      return;
    }

    if (kDebugMode) {
      print('LocationNotifier: Saving location for user: $userId');
      print('LocationNotifier: Location: ${state.city}, ${state.country}');
    }

    try {
      state = state.copyWith(isSaving: true);

      // Create user location object
      final userLocation = UserLocation(
        lat: state.latitude,
        lng: state.longitude,
        country: state.country,
        city: state.city,
        district: state.district,
      );

      // Update user location through user provider
      final success = await ref.read(userProvider.notifier).updateUserLocation(
            userId,
            userLocation,
          );

      if (success) {
        state = state.copyWith(isSaving: false);

        if (kDebugMode) {
          print('LocationNotifier: Location saved successfully');
        }

        // Show appropriate success message and navigate
        if (isFromSetting) {
          // Show update success message and go back
          // _showLocationUpdatedSuccessMessage();
          _navigateBack();
        } else {
          // Navigate to home screen (profile complete) during onboarding
          _navigateToHome();
        }
      } else {
        state = state.copyWith(
          isSaving: false,
          errorMessage: 'Failed to save location',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        errorMessage: 'An error occurred while saving location',
      );

      if (kDebugMode) {
        print('LocationNotifier: Error saving location - $e');
      }

      ToastCustom.errorToast('حدث خطأ أثناء حفظ الموقع');
    }
  }

  /// Navigate to home screen
  void _navigateToHome() {
    if (kDebugMode) {
      print('LocationNotifier: Navigating to home screen');
    }

    ref.read(navigationProvider.notifier).navigateToAndClearAll(AppRoutes.home);
  }

  /// Navigate back (when coming from settings)
  void _navigateBack() {
    if (kDebugMode) {
      print('LocationNotifier: Navigating back to previous screen');
    }

    ref.read(navigationProvider.notifier).goBack();
  }

  /// Open app settings
  void openAppSettings() {
    state = state.copyWith(isOpenAppSetting: true);

    if (kDebugMode) {
      print('LocationNotifier: Opening app settings');
    }

    // In real implementation, this would open device settings
    ToastCustom.successToast('جاري فتح إعدادات التطبيق...');
  }

  /// Close app settings dialog
  void closeAppSettings() {
    state = state.copyWith(isOpenAppSetting: false);
  }

  /// Check if current city supports districts and load them
  Future<void> _checkAndLoadDistricts() async {
    if (state.city.isEmpty) {
      if (kDebugMode) {
        print('LocationNotifier: No city to check for districts');
      }
      // Set district check complete if no city to check
      state = state.copyWith(districtCheckComplete: true);
      return;
    }

    try {
      if (kDebugMode) {
        print(
            'LocationNotifier: Starting district check for city: "${state.city}"');
      }

      // First check if city is supported
      final cityValidationNotifier = ref.read(cityValidationProvider.notifier);
      final isSupported = await cityValidationNotifier.validateCity(state.city);

      if (kDebugMode) {
        print(
            'LocationNotifier: City validation result: ${isSupported.isSupported}');
      }

      if (!isSupported.isSupported) {
        if (kDebugMode) {
          print(
              'LocationNotifier: City "${state.city}" is not supported, no districts to load');
        }
        // Set district check complete even if city is not supported
        state = state.copyWith(districtCheckComplete: true);
        return;
      }

      // Map Arabic city names to English keys for district lookup
      final cityKey = _getCityKeyFromName(state.city);

      if (kDebugMode) {
        print(
            'LocationNotifier: Mapped city "${state.city}" to key: "$cityKey"');
      }

      if (cityKey.isEmpty) {
        if (kDebugMode) {
          print('LocationNotifier: Could not map city to a valid key');
        }
        // Set district check complete even if city key is empty
        state = state.copyWith(districtCheckComplete: true);
        return;
      }

      state = state.copyWith(isLoadingDistricts: true);

      if (kDebugMode) {
        print('LocationNotifier: Loading districts for city key: $cityKey');
      }

      // Load districts using district provider
      await ref.read(districtProvider.notifier).loadDistrictsForCity(cityKey);

      // Read the district state after loading
      final districtState = ref.read(districtProvider);

      if (kDebugMode) {
        print('LocationNotifier: District state after loading:');
        print(
            '  - Available districts: ${districtState.availableDistricts.length}');
        print('  - Districts loaded: ${districtState.districtsLoaded}');
        print('  - Current city key: ${districtState.currentCityKey}');
        print('  - Error message: ${districtState.errorMessage}');
      }

      final hasDistricts = districtState.availableDistricts.isNotEmpty;

      state = state.copyWith(
        citySupportsDistricts: hasDistricts,
        availableDistricts: districtState.availableDistricts,
        isLoadingDistricts: false,
        districtCheckComplete: true, // Mark district check as complete
      );

      if (kDebugMode) {
        print('LocationNotifier: City supports districts: $hasDistricts');
        if (hasDistricts) {
          print(
              'LocationNotifier: Found ${districtState.availableDistricts.length} districts');
          for (final district in districtState.availableDistricts) {
            print(
                'LocationNotifier: District - ${district.nameAr} (${district.nameEn})');
          }
        } else {
          print('LocationNotifier: No districts found for city key: $cityKey');
        }
      }
    } catch (e) {
      state = state.copyWith(
        citySupportsDistricts: false,
        availableDistricts: [],
        isLoadingDistricts: false,
        districtCheckComplete:
            true, // Mark district check as complete even on error
      );

      if (kDebugMode) {
        print('LocationNotifier: Error checking districts for city - $e');
      }
    }
  }

  /// Map city name to city key for district lookup
  String _getCityKeyFromName(String cityName) {
    // Map common Arabic city names to their English keys
    final cityMappings = {
      'الرياض': 'riyadh',
      'riyadh': 'riyadh',
      'جدة': 'jeddah',
      'jeddah': 'jeddah',
      'الدمام': 'dammam',
      'dammam': 'dammam',
      'مكة': 'mecca',
      'mecca': 'mecca',
      'المدينة': 'medina',
      'medina': 'medina',
      'الطائف': 'taif',
      'taif': 'taif',
    };

    final normalizedCityName = cityName.toLowerCase().trim();

    // First try direct mapping
    if (cityMappings.containsKey(normalizedCityName)) {
      return cityMappings[normalizedCityName]!;
    }

    // If no direct mapping, try to find by Arabic name
    for (final entry in cityMappings.entries) {
      if (entry.key == cityName.trim()) {
        return entry.value;
      }
    }

    // Fallback: convert to lowercase and replace spaces with underscores
    return normalizedCityName.replaceAll(' ', '_');
  }

  /// Select a district manually
  void selectDistrict(district_service.District district) {
    if (kDebugMode) {
      print('LocationNotifier: Selecting district: ${district.nameAr}');
      if (district.hasValidCoordinates) {
        print(
            'LocationNotifier: District has coordinates: ${district.coordinatesString}');
      } else {
        print(
            'LocationNotifier: District has no coordinates, keeping current location');
      }
    }

    // Update district name and coordinates if available
    if (district.hasValidCoordinates) {
      state = state.copyWith(
        district: district.nameAr,
        latitude: district.latitude!,
        longitude: district.longitude!,
      );

      if (kDebugMode) {
        print(
            'LocationNotifier: Updated location to district coordinates: ${district.latitude}, ${district.longitude}');
      }
    } else {
      // Only update district name if no coordinates available
      state = state.copyWith(district: district.nameAr);

      if (kDebugMode) {
        print(
            'LocationNotifier: Updated district name only, keeping current coordinates');
      }
    }
  }

  /// Try to match GPS-captured district with Firestore district to get coordinates
  Future<void> _matchGpsDistrictWithFirestore() async {
    if (state.district.isEmpty || state.city.isEmpty) {
      if (kDebugMode) {
        print('LocationNotifier: No district or city to match');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print(
            'LocationNotifier: Attempting to match GPS district "${state.district}" with Firestore');
      }

      // Get city key for district lookup
      final cityKey = _getCityKeyFromName(state.city);
      if (cityKey.isEmpty) {
        if (kDebugMode) {
          print(
              'LocationNotifier: Could not get city key for district matching');
        }
        return;
      }

      // Find matching district in Firestore
      final districtService = ref.read(districtServiceProvider);
      final matchedDistrict =
          await districtService.findDistrictByName(cityKey, state.district);

      if (matchedDistrict != null && matchedDistrict.hasValidCoordinates) {
        // Update coordinates with district coordinates
        state = state.copyWith(
          latitude: matchedDistrict.latitude!,
          longitude: matchedDistrict.longitude!,
          district: matchedDistrict
              .nameAr, // Use the standardized name from Firestore
        );

        if (kDebugMode) {
          print(
              'LocationNotifier: Matched GPS district with Firestore district: ${matchedDistrict.nameAr}');
          print(
              'LocationNotifier: Updated coordinates to: ${matchedDistrict.latitude}, ${matchedDistrict.longitude}');
        }
      } else {
        if (kDebugMode) {
          print(
              'LocationNotifier: No matching district found in Firestore or district has no coordinates');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'LocationNotifier: Error matching GPS district with Firestore - $e');
      }
      // Don't throw error, just continue with GPS coordinates
    }
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for current latitude
@Riverpod(keepAlive: true)
double currentLatitude(CurrentLatitudeRef ref) {
  return ref.watch(locationProvider).latitude;
}

/// Provider for current longitude
@Riverpod(keepAlive: true)
double currentLongitude(CurrentLongitudeRef ref) {
  return ref.watch(locationProvider).longitude;
}

/// Provider for current city
@Riverpod(keepAlive: true)
String currentCity(CurrentCityRef ref) {
  return ref.watch(locationProvider).city;
}

/// Provider for current country
@Riverpod(keepAlive: true)
String currentCountry(CurrentCountryRef ref) {
  return ref.watch(locationProvider).country;
}

/// Provider for location loading state
@Riverpod(keepAlive: true)
bool isLocationLoading(IsLocationLoadingRef ref) {
  return ref.watch(locationProvider).isLoading;
}

/// Provider for location saving state
@Riverpod(keepAlive: true)
bool isLocationSaving(IsLocationSavingRef ref) {
  return ref.watch(locationProvider).isSaving;
}

/// Provider for location permission status
@Riverpod(keepAlive: true)
bool hasLocationPermission(HasLocationPermissionRef ref) {
  return ref.watch(locationProvider).hasLocationPermission;
}

/// Provider for location data availability
@Riverpod(keepAlive: true)
bool hasLocationData(HasLocationDataRef ref) {
  return ref.watch(locationProvider).hasLocationData;
}

/// Provider for location error message
@Riverpod(keepAlive: true)
String? locationErrorMessage(LocationErrorMessageRef ref) {
  return ref.watch(locationProvider).errorMessage;
}
